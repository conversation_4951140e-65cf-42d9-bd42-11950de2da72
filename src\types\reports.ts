import React from "react";
import { SelectChangeEvent } from "@mui/material";
import { TimelogData } from "./tracker";

export interface ProjectDisplayProps {
  projectId: string;
}

export interface TaskDisplayProps {
  taskId: string;
  projectId: string;
}

export interface DetailedTrackerReportProps {
  dateRange: { start: Date; end: Date };
  setDateRange: React.Dispatch<React.SetStateAction<{ start: Date; end: Date }>>;
  selectedProjects: string[];
  handleProjectsChange: (event: SelectChangeEvent<string[]>) => void;
  selectedUsers: string[];
  handleUsersChange: (event: SelectChangeEvent<string[]>) => void;
  billableFilter: boolean;
  setBillableFilter: React.Dispatch<React.SetStateAction<boolean>>;
  otFilter: boolean;
  setOtFilter: React.Dispatch<React.SetStateAction<boolean>>;
  filteredLogs: TimelogData[];
}

export interface User {
  user_id: string;
  full_name: string;
  total_duration: string;
  total_duration_second: number;
}

export interface ProjectSummary {
  project_id: string;
  project_name: string;
  users: User[];
}

export interface WeeklySummaryProps {
  dateRange: { start: Date; end: Date };
  setDateRange: React.Dispatch<React.SetStateAction<{ start: Date; end: Date }>>;
  selectedProjects: string[];
  handleProjectsChange: (event: SelectChangeEvent<string[]>) => void;
  selectedUsers: string[];
  handleUsersChange: (event: SelectChangeEvent<string[]>) => void;
  billableFilter: boolean;
  setBillableFilter: React.Dispatch<React.SetStateAction<boolean>>;
  otFilter: boolean;
  setOtFilter: React.Dispatch<React.SetStateAction<boolean>>;
}

// Define a generic option type
export interface AutocompleteOption {
  label: string;
  value: string;
  isPending?: boolean;
}

// Define props for the generic hook
export interface UseInfiniteAutocompleteOptions<T extends AutocompleteOption> {
  options: T[];
  hasNextPage: boolean | undefined;
  fetchNextPage: () => void;
  isFetchingNextPage: boolean;
  isLoading: boolean;
  isError: boolean;
  retry: () => void;
}

export interface AutocompleteInfiniteProps<T extends AutocompleteOption> {
  value: T["value"][];
  onChange: (value: T["value"][]) => void;
  useInfiniteOptions: (searchTerm: string) => UseInfiniteAutocompleteOptions<T>;
  label?: string;
  placeholder?: string;
  multiple?: boolean;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
}

export interface ReportFiltersProps {
  dateRange: { start: Date; end: Date };
  setDateRange: (value: { start: Date; end: Date }) => void;
  selectedProjects: string[];
  handleProjectsChange: (event: SelectChangeEvent<string[]>) => void;
  selectedUsers: string[];
  handleUsersChange: (event: SelectChangeEvent<string[]>) => void;
  billableFilter: boolean;
  setBillableFilter: (value: boolean) => void;
  otFilter: boolean;
  setOtFilter: (value: boolean) => void;
}
