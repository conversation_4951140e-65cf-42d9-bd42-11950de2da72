export interface ProjectOption {
  label: string;
  value: string;
  space_id?: string | null;
  isPending?: boolean;
  status?: string;
}

export interface ProjectResponse {
  project: {
    id: string;
    name: string;
  };
}

export interface FormValues {
  project: string;
  clickupTaskName: string;
  description: string;
  isBillable: boolean;
  isOT: boolean;
}

export interface ProjectData {
  project?: { name: string; id: string };
  name?: string;
  id?: string;
  clickup_space_id?: string | null;
}

export interface TaskData {
  id: string;
  name: string;
  description: string;
  status: string;
  task_created_date: string;
  task_id: string;
  folder_name: string;
  space_name: string;
  sub_task: TaskData[];
  time_estimate: number | null;
  start_date: string | null;
  due_date: string | null;
  is_archived: boolean;
}

export interface ClickUpTasksResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: TaskData[];
}

export interface TimelogData {
  id: string;
  user: string;
  project: string;
  description: string;
  clickup_task_id?: string;
  start_time: string;
  end_time?: string;
  is_billable: boolean;
  is_ot: boolean;
}

export interface TaskInputProps {
  inputOptions: ProjectOption[];
  name: string;
  label?: string;
  onSelectProject?: (project: ProjectOption | null) => void;
  onInputChange: (value: string) => void;
  disabled?: boolean;

  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  onLoadMore?: () => void;

  error?: boolean;
  errorMessage?: string;
  onRetry?: () => void;

  onFieldUpdate?: (field: string, value: string | boolean) => void;

  specificTask?: TaskData | null;
}

export interface DaysTaskProps {
  theme: import("@mui/material/styles").Theme;
}

export interface ProjectAutocompleteProps {
  userId: string;
  value: string;
  onChange: (value: string) => void;
  label?: string;
  error?: boolean;
  helperText?: string;
}

export interface ITaskInputProps {
  isInput: boolean;
  timeSheet: Partial<import("@/interfaces/trackifyCore.interface").ITimesheet>;
  borderRadius: number;
  projectList?: SelectOption[];
}

export interface SelectOption {
  id: string;
  label: string;
}

export interface ITask {
  id: string;
  task_id: string;
  name: string;
  sub_task: ITask[];
}

export interface TimelogsQueryData {
  data: TimelogData[];
  [key: string]: unknown;
}

export interface WeeklyTimelogData {
  week: string;
  total_hours: number;
  logs: TimelogData[];
}

export interface WeeklyTimelogsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: WeeklyTimelogData[];
}

export interface EditData {
  project: string;
  description: string;
  start_time: string;
  end_time: string;
  is_billable: boolean;
  is_ot: boolean;
  clickup_task_id: string;
  [key: string]: string | boolean;
}

export interface EditableTimelogEntryProps {
  log: TimelogData;
  onRestartTimer?: (values: FormValues) => Promise<void>;
  isTracking?: boolean;
}

export interface WeeklyTimelogCardProps {
  weeklyData: WeeklyTimelogData;
  onRestartTimer?: (values: FormValues) => Promise<void>;
  isTracking?: boolean;
}

export interface WeeklyTimelogListProps {
  userId: string | undefined;
  showWeekPicker?: boolean;
  singleWeekView?: boolean;
  initialDate?: Date;
  onRestartTimer?: (values: FormValues) => Promise<void>;
}

export interface FieldChange {
  field: string;
  oldValue: string;
  newValue: string;
}

export interface UpdateConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  oldData: TimelogData;
  newData: TimelogData;
  loading: boolean;
  formatValue?: (field: string, value: unknown) => string;
}

export interface PreviousTimelogs {
  pages: WeeklyTimelogData[][];
}

export interface MutationContext {
  previousTimelogs?: PreviousTimelogs | undefined;
}

export interface UpdateTimelogData {
  project?: string;
  description?: string;
  start_time?: string;
  end_time?: string | null;
  is_billable?: boolean;
  is_ot?: boolean;
  clickup_task_id?: string | null;

  id?: string;
  user?: string;
  duration_seconds?: number;
  week_start_date?: string;
  created_at?: string;
  updated_at?: string;
}

export interface UpdateTimelogParams {
  timelogId: string;
  updateData: UpdateTimelogData;

  silent?: boolean;
}

export interface UseWeeklyTimelogsParams {
  userId?: string;
  user__in?: string[];
  page?: number;
  pageSize?: number;
  start_time__gte?: string;
  end_time__lte?: string;
  paginate?: boolean;
  project?: string;
  project__in?: string;
  is_billable?: boolean;
  is_ot?: boolean;
}

export interface DailyTimelogGroupProps {
  date: string;
  logs: TimelogData[];
  onRestartTimer?: (values: FormValues) => Promise<void>;
  isTracking?: boolean;
}

export interface TimelogEntryProps {
  log: TimelogData;
}

export interface WeekPickerProps {
  selectedDate: Date;
  onWeekChange: (date: Date) => void;
  weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  showTodayButton?: boolean;
}
