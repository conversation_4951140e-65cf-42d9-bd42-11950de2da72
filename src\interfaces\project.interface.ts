import { ProjectStatus } from "@/enums/enum";

export const statusLabels: Record<ProjectStatus, string> = {
  [ProjectStatus.ACTIVE]: "Active",
  [ProjectStatus.ON_HOLD]: "On Hold",
  [ProjectStatus.COMPLETE]: "Complete",
  [ProjectStatus.CANCELED]: "Canceled",
};

export interface ProjectFormErrors {
  name?: string | null;
  color_hex_value?: string | null;
  is_public?: string | null;
  is_billable?: string | null;
  clickup_space_id?: string | null;
  status?: ProjectStatus;
  project_type?: ProjectType | null;
}

export interface APIError {
  response?: {
    status?: number;
    data?: Record<string, string>;
  };
}
export interface ProjectRole {
  id: string;
  name: string;
}
export interface Project {
  id: string | null;
  color_hex_value: string;
  name: string;
  is_public: boolean;
  is_billable: boolean;
  clickup_space_id?: string;
  project_type: ProjectType;
  status: ProjectStatus;
  client: string;
  project_roles: string;
}
export interface ProjectType {
  id: string;
  name: string;
}
export interface TableRowsLoaderProps {
  rowsNum: number;
}

export interface ProjectFormProps {
  project: Project | null;
  onSubmit: (data: ProjectFormData) => void;
}
export interface ProjectTypeFormProps {
  projectType: ProjectType | null;
  callback: () => void;
}
export interface ProjectMemberProps {
  project_id: string | null;
}
export interface ProjectFormData {
  id?: string;
  name: string;
  color_hex_value: string;
  is_public: boolean;
  is_billable: boolean;
  clickup_space_id?: string;
  project_type: string | null;
  status: string | null;
}

export interface ProjectTypeFormData {
  name: string;
  id?: string;
}

export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  permissions: string[];
  is_invited: boolean;
  groups: Group[];
}
export interface ProjectUser {
  id: string;
  project: Project;
  user: User;
  is_manager: boolean;
  assigned_type: string;
  last_worked_at: string;
  project_role?: string;
}

export interface UserAssignFormValues {
  selectedUser: User | null;
  selectedMember: Member | null;
  assigned_type: string;
  tech_stack: string[];
  project_role?: ProjectRole;
}

export interface Group {
  name: string;
}

export interface Member {
  member_id: string;
  id: string;
  user: User;
  assigned_type: string;
  tech_stack: TechStack[];
  last_worked_at: string | null;
  project_role?: ProjectRole;
}

export interface TechStack {
  name: string;
}

export interface Client {
  id: string;
  name: string;
  status: "active" | "archived";
  description: string;
  image?: string;
  email: string;
  address: string;
  country: string;
  phone: string;
}

export interface AssignUserPayload {
  project: string;
  user: string;
  assigned_type: string;
  tech_stack: string[];
  project_role: string;
}

export interface UpdateUserPayload {
  assigned_type: string;
  tech_stack: string[];
  project_role: string;
}

export interface UpdateUserVariables {
  id: string;
  payload: UpdateUserPayload;
}
