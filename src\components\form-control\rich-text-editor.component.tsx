import React from "react";
import { Editor } from "@tinymce/tinymce-react";
import { Box, Typography } from "@mui/material";

interface RichTextEditorProps {
  value?: string;
  onChange?: (content: string) => void;
  label?: string;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  helperText?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value = "",
  onChange,
  label,
  placeholder,
  height = 200,
  disabled = false,
  required = false,
  error = false,
  helperText,
}) => {
  const handleEditorChange = (content: string) => {
    if (onChange) {
      onChange(content);
    }
  };

  return (
    <Box sx={{ width: "100%" }}>
      {label && (
        <Typography
          variant="body2"
          component="label"
          sx={{
            display: "block",
            mb: 1,
            fontWeight: 500,
            color: error ? "error.main" : "text.primary",
          }}
        >
          {label}
          {required && (
            <Typography component="span" sx={{ color: "error.main", ml: 0.5 }}>
              *
            </Typography>
          )}
        </Typography>
      )}
      <Box
        sx={{
          border: error ? "1px solid" : "1px solid",
          borderColor: error ? "error.main" : "grey.300",
          borderRadius: 1,
          "&:hover": {
            borderColor: error ? "error.main" : "primary.main",
          },
          "&:focus-within": {
            borderColor: error ? "error.main" : "primary.main",
            borderWidth: 2,
          },
        }}
      >
        <Editor
          apiKey={import.meta.env.VITE_TINYMCE_API_KEY}
          value={value}
          disabled={disabled}
          init={{
            height,
            menubar: false,
            placeholder,
            plugins: [
              "advlist",
              "autolink",
              "lists",
              "link",
              "image",
              "charmap",
              "preview",
              "anchor",
              "searchreplace",
              "visualblocks",
              "code",
              "fullscreen",
              "insertdatetime",
              "media",
              "table",
              "help",
              "wordcount",
            ],
            toolbar:
              "undo redo | blocks | " +
              "bold italic forecolor | alignleft aligncenter " +
              "alignright alignjustify | bullist numlist outdent indent | " +
              "removeformat | help",
            content_style:
              "body { font-family:Helvetica,Arial,sans-serif; font-size:14px }",
            branding: false,
            resize: false,
            statusbar: false,
          }}
          onEditorChange={handleEditorChange}
        />
      </Box>
      {helperText && (
        <Typography
          variant="caption"
          sx={{
            display: "block",
            mt: 0.5,
            color: error ? "error.main" : "text.secondary",
          }}
        >
          {helperText}
        </Typography>
      )}
    </Box>
  );
};

export default RichTextEditor;
