import React, { useEffect } from "react";
import {
  Button,
  Checkbox,
  FormControl,
  FormHelperText,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import GenericFormControl from "@/components/form-control/generic-form.component";
import { useFormikContext } from "formik";
import { Group, IGroup, IUserForm } from "@/interfaces/teams.interface";
import { useQuery, keepPreviousData } from "@tanstack/react-query";
import Resource from "@/core/api/resource";
import { ITeamMemberFormProps } from "@/interface/team";
import { Country } from "@/types/teams";

const TeamMemberForm: React.FC<ITeamMemberFormProps> = props => {
  const userResource = new Resource("auth/groups");
  const countryResource = new Resource("users/country");
  const { values, setFieldValue, errors, touched } = useFormikContext<IUserForm>();

  const { data: allGroupsList } = useQuery({
    queryKey: ["auth/groups"],
    queryFn: async () => {
      const response = await userResource.list();
      return {
        rows: response.data.results,
        count: response.data.count,
      };
    },
    placeholderData: keepPreviousData,
  });

  const { data: countriesList } = useQuery({
    queryKey: ["users/country"],
    queryFn: async () => {
      const response = await countryResource.list();
      return response.data;
    },
    placeholderData: keepPreviousData,
  });

  const [groups, setGroups] = React.useState<string[]>([]);

  const handleChange = (event: SelectChangeEvent<typeof groups>) => {
    const {
      target: { value },
    } = event;

    setGroups(typeof value === "string" ? value.split(",") : value);
    const selectedIds: string[] = typeof value === "string" ? value.split(",") : value;
    const selectedGroups = allGroupsList?.rows?.filter((group: IGroup) => selectedIds.includes(group.id));

    setFieldValue("groups", selectedGroups);
  };

  const handleCountryChange = (event: SelectChangeEvent<string>) => {
    const countryCode = event.target.value;
    setFieldValue("country", countryCode);
  };

  useEffect(() => {
    if (values?.groups) {
      setGroups(values.groups.map((group: Group) => group.id));
    }
  }, [values?.groups]);

  return (
    <>
      <GenericFormControl
        id="firstName"
        label="First name"
        type="text"
        value={values?.first_name}
        onChangeFn={e => setFieldValue("first_name", e.target.value)}
        error={touched.first_name && Boolean(errors.first_name)}
      />
      <GenericFormControl
        id="middlename"
        label="Middle name"
        type="text"
        value={values?.middle_name}
        onChangeFn={e => setFieldValue("middle_name", e.target.value)}
        error={touched.middle_name && Boolean(errors.middle_name)}
      />
      <GenericFormControl
        id="lastName"
        label="Last name"
        type="text"
        value={values?.last_name}
        onChangeFn={e => setFieldValue("last_name", e.target.value)}
        error={touched.last_name && Boolean(errors.last_name)}
      />
      <GenericFormControl
        id="phone"
        label="Phone"
        type="text"
        value={values?.phone}
        onChangeFn={e => setFieldValue("phone", e.target.value)}
        error={touched.phone && Boolean(errors.phone)}
      />
      <FormControl fullWidth margin="normal" error={Boolean(errors.country && touched.country)}>
        <InputLabel id="country-select-label">Country</InputLabel>
        <Select
          labelId="country-select-label"
          id="country"
          value={values?.country || ""}
          label="Country"
          onChange={handleCountryChange}
        >
          {countriesList?.map((country: Country) => (
            <MenuItem key={country.name} value={country.name}>
              {country.display_name}
            </MenuItem>
          ))}
        </Select>
        {Boolean(errors.country && touched.country) && (
          <FormHelperText>
            {typeof errors.country === "string" ? errors.country : "Please select a country"}
          </FormHelperText>
        )}
      </FormControl>
      <GenericFormControl
        id="email"
        label="Email"
        type="email"
        value={values?.email}
        onChangeFn={e => setFieldValue("email", e.target.value)}
        error={touched.email && Boolean(errors.email)}
      />

      <FormControl fullWidth margin="normal" error={Boolean(errors.groups && touched.groups)}>
        <InputLabel
          id="role-multiple-checkbox-label"
          sx={{
            color: "gray",
            "&.Mui-focused": {
              color: "gray",
            },
          }}
        >
          Role
        </InputLabel>
        <Select
          labelId="group-multiselect-label"
          id="groups"
          multiple
          value={groups}
          onChange={handleChange}
          input={<OutlinedInput label="Select Role" sx={{ color: "black" }} />}
          renderValue={selected =>
            allGroupsList?.rows
              .filter((group: Group) => selected.includes(group.id))
              .map((group: Group) => group.name)
              .join(", ")
          }
          sx={{
            "& .MuiSelect-select": {
              color: "black",
            },
            "&. MuiCheckbox-root": {
              fill: "black",
            },
          }}
        >
          {allGroupsList?.rows.map((group: Group) => {
            return (
              <MenuItem key={group?.id} value={group?.id}>
                <Checkbox checked={groups.includes(group?.id)} />
                <ListItemText primary={group?.name_display} />
              </MenuItem>
            );
          })}
        </Select>
        {Boolean(errors.groups && touched.groups) && (
          <FormHelperText>
            {typeof errors.groups === "string" ? errors.groups : "Invalid group selection"}
          </FormHelperText>
        )}
      </FormControl>

      <Button
        variant="contained"
        type="submit"
        sx={{
          mt: 2,
          px: 2,
          py: 1.5,
          width: "100%",
          fontWeight: 600,
          fontSize: "1rem",
          textTransform: "none",
          borderRadius: 2,
          boxShadow: "0px 3px 6px rgba(0, 0, 0, 0.1)",
          backgroundColor: "custom.brand.primary",
          color: "white",
          "&:hover": {
            backgroundColor: "custom.brand.primary",
            filter: "brightness(0.95)",
          },
        }}
      >
        {props?.selectedData?.id ? "Update" : " Save "}
      </Button>
    </>
  );
};

export default TeamMemberForm;
