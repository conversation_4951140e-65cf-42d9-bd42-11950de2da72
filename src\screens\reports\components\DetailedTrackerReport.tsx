import React from "react";
import { Box, Typography, List, ListItem, ListItemText, Paper, Chip, Divider } from "@mui/material";
import { format } from "date-fns";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { DetailedTrackerReportProps } from "@/types/reports";
import ProjectDisplay from "@/components/reports/ProjectDisplay";
import TaskDisplay from "@/components/reports/TaskDisplay";

function DetailedTrackerReport({ filteredLogs }: Pick<DetailedTrackerReportProps, "filteredLogs">) {
  // Transform filtered logs into chart data
  const chartData = React.useMemo(() => {
    const dailyData: { [key: string]: number } = {};

    filteredLogs.forEach(log => {
      if (log.end_time) {
        const date = format(new Date(log.start_time), "yyyy-MM-dd");
        const startTime = new Date(log.start_time);
        const endTime = new Date(log.end_time);
        const durationHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);

        dailyData[date] = (dailyData[date] || 0) + durationHours;
      }
    });

    return Object.entries(dailyData)
      .sort(([dateA], [dateB]) => new Date(dateA).getTime() - new Date(dateB).getTime())
      .map(([date, hours]) => ({
        date: format(new Date(date), "MMM dd"),
        hours: Number(hours.toFixed(2)),
      }));
  }, [filteredLogs]);

  // Format duration helper
  const formatDuration = (startTime: string, endTime: string | undefined) => {
    if (!endTime) return "Ongoing";
    const start = new Date(startTime);
    const end = new Date(endTime);
    const durationMs = end.getTime() - start.getTime();
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  // Sort filtered logs by start time (most recent first)
  const sortedLogs = React.useMemo(() => {
    return [...filteredLogs].sort((a, b) => new Date(b.start_time).getTime() - new Date(a.start_time).getTime());
  }, [filteredLogs]);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Time Tracking Summary Chart
      </Typography>

      {chartData.length === 0 ? (
        <Typography variant="body1" color="text.secondary">
          No data available for the selected filters.
        </Typography>
      ) : (
        <Box>
          <Box sx={{ width: "100%", height: 400, mb: 4 }}>
            <ResponsiveContainer>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis label={{ value: "Hours", angle: -90, position: "insideLeft" }} />
                <Tooltip formatter={value => [`${value} hours`, "Duration"]} />
                <Line type="monotone" dataKey="hours" stroke="#8884d8" strokeWidth={2} dot={{ fill: "#8884d8" }} />
              </LineChart>
            </ResponsiveContainer>
          </Box>

          {/* Detailed Time Logs List */}
          <Typography variant="h6" gutterBottom>
            Detailed Time Logs
          </Typography>
          <Paper variant="outlined" sx={{ maxHeight: 500, overflow: "auto" }}>
            <List>
              {sortedLogs.map((log, index) => (
                <React.Fragment key={`${log.start_time}-${log.project}-${index}`}>
                  <ListItem>
                    <ListItemText
                      primary={
                        <Box sx={{ display: "flex", alignItems: "center", gap: 1, flexWrap: "wrap" }}>
                          <Typography variant="subtitle1" component="span">
                            <ProjectDisplay projectId={log.project} />
                          </Typography>
                          <Chip
                            label={formatDuration(log.start_time, log.end_time)}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                          {log.is_billable && <Chip label="Billable" size="small" color="success" variant="outlined" />}
                          {log.is_ot && <Chip label="OT" size="small" color="warning" variant="outlined" />}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary" component="div">
                            {format(new Date(log.start_time), "MMM dd, yyyy - HH:mm")} -{" "}
                            {log.end_time ? format(new Date(log.end_time), "HH:mm") : "Ongoing"}
                          </Typography>
                          {log.clickup_task_id && <TaskDisplay taskId={log.clickup_task_id} projectId={log.project} />}
                          {log.description && (
                            <Typography variant="body2" color="text.secondary" component="span">
                              {log.description}
                            </Typography>
                          )}
                        </Box>
                      }
                      slotProps={{ secondary: { component: "div" } }}
                    />
                  </ListItem>
                  {index < sortedLogs.length - 1 && <Divider />}
                </React.Fragment>
              ))}
              {sortedLogs.length === 0 && (
                <ListItem>
                  <ListItemText
                    primary={
                      <Typography variant="body1" color="text.secondary" align="center">
                        No time logs found for the selected filters.
                      </Typography>
                    }
                  />
                </ListItem>
              )}
            </List>
          </Paper>
        </Box>
      )}
    </Box>
  );
}

export default DetailedTrackerReport;
