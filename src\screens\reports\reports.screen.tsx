import React, { useState } from "react";
import { Box, Tabs, Tab, Typography, Paper, CircularProgress, SelectChangeEvent } from "@mui/material";
import { Download as DownloadIcon } from "@mui/icons-material";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { useWeeklyTimelogs, useExportTimelogs } from "@/hooks/tracker";
import { useUserProfile } from "@/hooks/tracker/useUserProfile";
import { format, startOfWeek, endOfWeek } from "date-fns";
import WeeklySummary from "./components/WeeklySummary";
import DetailedTrackerReport from "./components/DetailedTrackerReport";
import ReportFilters from "./components/ReportFilters";
import GenericActionButtons from "@/components/form-control/generic-action-buttons.component";

function ReportsScreen() {
  const { data: { id: userId } = {} } = useUserProfile();
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState({ start: startOfWeek(new Date()), end: endOfWeek(new Date()) });
  const [billableFilter, setBillableFilter] = useState(false);
  const [otFilter, setOtFilter] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  // Export functionality
  const exportMutation = useExportTimelogs();

  const { data: weeklyTimelogs, isLoading: weeklyLoading } = useWeeklyTimelogs({
    userId: selectedUsers.length === 0 ? userId : undefined,
    user__in: selectedUsers.length > 0 ? selectedUsers : undefined,
    paginate: false,
    start_time__gte: format(dateRange.start, "yyyy-MM-dd"),
    end_time__lte: format(dateRange.end, "yyyy-MM-dd"),
    ...(selectedProjects.length > 0 && { project__in: selectedProjects.join(",") }),
    ...(billableFilter && { is_billable: true }),
    ...(otFilter && { is_ot: true }),
  });

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleProjectsChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedProjects(typeof value === "string" ? value.split(",") : value);
  };

  const handleUsersChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value;
    setSelectedUsers(typeof value === "string" ? value.split(",") : value);
  };

  const handleExport = () => {
    exportMutation.mutate({
      userId: selectedUsers.length === 0 ? userId : undefined,
      user__in: selectedUsers.length > 0 ? selectedUsers : undefined,
      start_time__gte: format(dateRange.start, "yyyy-MM-dd"),
      end_time__lte: format(dateRange.end, "yyyy-MM-dd"),
      project__in: selectedProjects.length > 0 ? selectedProjects : undefined,
      is_billable: billableFilter || undefined,
      is_ot: otFilter || undefined,
    });
  };

  if (weeklyLoading) {
    return <CircularProgress />;
  }

  const filteredLogs = weeklyTimelogs?.results?.flatMap(week => week.logs) || [];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom mb={4}>
          Reports
        </Typography>
        <ReportFilters
          dateRange={dateRange}
          setDateRange={setDateRange}
          selectedProjects={selectedProjects}
          handleProjectsChange={handleProjectsChange}
          selectedUsers={selectedUsers}
          handleUsersChange={handleUsersChange}
          billableFilter={billableFilter}
          setBillableFilter={setBillableFilter}
          otFilter={otFilter}
          setOtFilter={setOtFilter}
        />
        <GenericActionButtons
          buttons={[
            {
              label: exportMutation.isPending ? "Exporting..." : "Export CSV",
              onClick: handleExport,
              variant: "contained",
              color: "primary",
              startIcon: <DownloadIcon />,
              disabled: exportMutation.isPending,
            },
          ]}
        />
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="report tabs">
          <Tab label="Weekly Summary" />
          <Tab label="Detailed Tracker Report" />
        </Tabs>
        <Paper sx={{ mt: 2, p: 2 }}>
          {tabValue === 0 && (
            <WeeklySummary
              dateRange={dateRange}
              selectedProjects={selectedProjects}
              selectedUsers={selectedUsers}
              billableFilter={billableFilter}
              otFilter={otFilter}
            />
          )}
          {tabValue === 1 && <DetailedTrackerReport filteredLogs={filteredLogs} />}
        </Paper>
      </Box>
    </LocalizationProvider>
  );
}

export default ReportsScreen;
