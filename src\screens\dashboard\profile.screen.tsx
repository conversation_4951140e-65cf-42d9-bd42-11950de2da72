import React, { useState } from "react";
import {
  Box,
  Typography,
  Paper,
  Stack,
  TextField,
  Button,
  InputAdornment,
  IconButton,
  CircularProgress,
  Tooltip,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";

import ActiveBreadcrumb from "@/components/breadcrumbs/activeBreadcrumb";
import { User } from "@/interfaces/profile.interface";
import Resource from "@/core/api/resource";
import AuthResource from "@/core/api/auth";
import theme from "@/theme/theme";
import { blue } from "@mui/material/colors";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import UploadIcon from "@mui/icons-material/Upload";
import teamsService from "@/core/api/teams.service";

const ProfileScreen: React.FC = () => {
  const [avatarUrl, setAvatarUrl] = useState<string>("");
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const queryClient = useQueryClient();

  const {
    data: user,
    isLoading,
    isError,
  } = useQuery<User>({
    queryKey: ["userProfile"],
    queryFn: async (): Promise<User> => {
      const userResource = new Resource("users/my-profile");
      const response = await userResource.list();
      return response.data;
    },
  });

  const auth = new AuthResource();

  const passwordSchema = Yup.object().shape({
    current: Yup.string(),
    new: Yup.string()
      .min(8, "Password must be at least 8 characters.")
      .matches(/[a-z]/, "Must contain lowercase letter.")
      .matches(/[A-Z]/, "Must contain uppercase letter.")
      .matches(/[0-9]/, "Must contain a number.")
      .matches(/[^a-zA-Z0-9]/, "Must contain a special character."),
    confirm: Yup.string().oneOf([Yup.ref("new")], "Passwords must match."),
  });

  const getUserGroupDisplayNames = (): string => {
    if (!user?.groups || user.groups.length === 0) return "";
    return user.groups.map(group => group.name_display || group.name).join(", ");
  };

  const uploadAvatar = async (file: File) => {
    if (!user || !user.id) {
      toast.error("User not loaded. Cannot upload avatar.");
      return;
    }

    const formData = new FormData();
    formData.append("image", file);

    try {
      setUploadingAvatar(true);
      await new teamsService.TeamUserProfileResources().uploadImage(formData);
      toast.success("Profile image updated!");
      setAvatarUrl("");
      await queryClient.invalidateQueries({ queryKey: ["userProfile"] });
    } catch {
      toast.error("Failed to upload profile image.");
    } finally {
      setUploadingAvatar(false);
    }
  };

  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setAvatarUrl(URL.createObjectURL(file));
      await uploadAvatar(file);
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ p: 3, mt: 7 }}>
        <Typography>Loading profile...</Typography>
      </Box>
    );
  }

  if (isError || !user) {
    return (
      <Box sx={{ p: 3, mt: 7 }}>
        <Typography color="error">Error loading profile.</Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        p: 3,
        minHeight: "80vh",
        bgcolor: "background.default",
      }}
    >
      <Box mb={4}>
        <ActiveBreadcrumb
          color={theme.palette.primary.main}
          links={[
            { text: "Profile", link: "/profile" },
            { text: `${user.first_name} ${user.last_name}`, link: "/profile" },
          ]}
        />
      </Box>

      <Box sx={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
        {/* Avatar Box */}
        <Box sx={{ position: "relative", width: 150, height: 150, flexShrink: 0 }}>
          <Box
            sx={{
              width: "100%",
              height: "100%",
              borderRadius: "50%",
              border: "1px solid",
              borderColor: "divider",
              overflow: "hidden",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              bgcolor: "background.paper",
              cursor: "pointer",
            }}
            onClick={() => document.getElementById("avatar-upload")?.click()}
          >
            {avatarUrl ? (
              <img src={avatarUrl} alt="Profile" style={{ width: "100%", height: "100%", objectFit: "cover" }} />
            ) : user.detail?.image ? (
              <img
                src={`${user.detail.image}?${Date.now()}`}
                alt="Profile"
                style={{ width: "100%", height: "100%", objectFit: "cover" }}
              />
            ) : (
              <AccountCircleIcon sx={{ fontSize: 150, color: "text.secondary" }} />
            )}

            {uploadingAvatar && (
              <Box
                sx={{
                  position: "absolute",
                  inset: 0,
                  backgroundColor: "rgba(255,255,255,0.6)",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  borderRadius: "50%",
                }}
              >
                <CircularProgress />
              </Box>
            )}
          </Box>

          {/* Upload Icon outside the circle */}
          <Tooltip title="Change your avatar" arrow>
            <Box
              sx={{
                position: "absolute",
                top: "20%",
                right: -5,
                transform: "translateY(-50%)",
                backgroundColor: "white",
                borderRadius: "50%",
                padding: 0.5,
                boxShadow: 1,
                cursor: "pointer",
                zIndex: 10,
                "&:hover": { bgcolor: blue[50] },
              }}
              onClick={e => {
                e.stopPropagation();
                document.getElementById("avatar-upload")?.click();
              }}
            >
              <UploadIcon sx={{ color: blue[500], fontSize: 18 }} />
            </Box>
          </Tooltip>

          <input
            id="avatar-upload"
            type="file"
            accept="image/*"
            style={{ display: "none" }}
            onChange={handleAvatarChange}
            disabled={uploadingAvatar}
          />
        </Box>

        {/* User Info */}
        <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
          <Typography variant="h4">
            Name:{" "}
            <Typography component="span">
              {user.first_name} {user.last_name}
            </Typography>
          </Typography>
          <Typography variant="h4">
            Email: <Typography component="span">{user.email}</Typography>
          </Typography>
          <Typography variant="h4">
            Country: <Typography component="span">{user.detail?.country_display || ""}</Typography>
          </Typography>
          <Typography variant="h4">
            Phone: <Typography component="span">{user.detail?.phone || ""}</Typography>
          </Typography>

          <Typography variant="h4">
            Role: <Typography component="span">{getUserGroupDisplayNames()}</Typography>
          </Typography>
        </Box>
      </Box>

      {/* Change Password Section */}
      <Paper elevation={0} sx={{ mt: 4, maxWidth: 560, bgcolor: "transparent" }}>
        <Typography variant="h5" sx={{ mb: 2, fontWeight: 600 }}>
          Change Password
        </Typography>
        <Formik
          initialValues={{ current: "", new: "", confirm: "" }}
          validationSchema={passwordSchema}
          validateOnBlur={false}
          validateOnChange={false}
          onSubmit={async (values, { setSubmitting, resetForm }) => {
            if (!values.current || !values.new || !values.confirm) {
              toast.error("All fields are required.");
              setSubmitting(false);
              return;
            }
            try {
              await auth.changePassword(values.current, values.new, values.confirm);
              toast.success("Password changed successfully!");
              resetForm();
            } catch {
              toast.error("Failed to change password.");
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({ values, handleChange, handleBlur, errors, isSubmitting }) => (
            <Form autoComplete="off">
              <Stack spacing={3}>
                {["current", "new", "confirm"].map(field => (
                  <TextField
                    key={field}
                    name={field}
                    label={
                      field === "current" ? "Current Password" : field === "new" ? "New Password" : "Confirm Password"
                    }
                    type={showPassword[field as keyof typeof showPassword] ? "text" : "password"}
                    value={values[field as keyof typeof values]}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(errors[field as keyof typeof errors])}
                    helperText={
                      errors[field as keyof typeof errors] ||
                      (field === "new" ? "Minimum 8 chars · uppercase, lowercase, number, special char" : "")
                    }
                    size="small"
                    placeholder={
                      field === "current" ? "Current password" : field === "new" ? "New password" : "Confirm password"
                    }
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() =>
                              setShowPassword(prev => ({
                                ...prev,
                                [field]: !prev[field as keyof typeof prev],
                              }))
                            }
                            edge="end"
                            size="small"
                          >
                            {showPassword[field as keyof typeof showPassword] ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                ))}
                <Button
                  type="submit"
                  variant="contained"
                  disabled={isSubmitting}
                  sx={{
                    width: 130,
                    fontWeight: 750,
                    textTransform: "none",
                    bgcolor: blue[600],
                    "&:hover": { bgcolor: blue[700] },
                  }}
                >
                  {isSubmitting ? "Changing..." : "Change Now"}
                </Button>
              </Stack>
            </Form>
          )}
        </Formik>
      </Paper>
    </Box>
  );
};

export default ProfileScreen;
