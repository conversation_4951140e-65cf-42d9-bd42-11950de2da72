import { useMemo } from "react";
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
} from "@mui/material";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useWeeklySummaryReport } from "@/hooks/tracker/useWeeklySummaryReport";
import { WeeklySummaryProps, User } from "@/types/reports";

function WeeklySummary({
  dateRange,
  selectedProjects,
  selectedUsers,
  billableFilter,
  otFilter,
}: Omit<
  WeeklySummaryProps,
  "setDateRange" | "handleProjectsChange" | "handleUsersChange" | "setBillableFilter" | "setOtFilter"
>) {
  const { data: reportData = [], isLoading: loading } = useWeeklySummaryReport({
    dateRange,
    selectedProjects,
    selectedUsers,
    billableFilter,
    otFilter,
  });

  const formatDuration = (seconds: number) => {
    const days = Math.floor(seconds / (24 * 3600));
    const hours = Math.floor((seconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (days > 0) {
      return `${days} day${days > 1 ? "s" : ""}, ${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
    }
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getTotalProjectDuration = (users: User[]) => {
    const totalSeconds = users.reduce((sum, user) => sum + user.total_duration_second, 0);
    return formatDuration(totalSeconds);
  };

  // Prepare chart data
  const chartData = useMemo(() => {
    return reportData.map((project, index) => {
      const totalSeconds = project.users.reduce((sum, user) => sum + user.total_duration_second, 0);
      const totalHours = totalSeconds / 3600;
      return {
        name: project.project_name,
        value: Number(totalHours.toFixed(2)),
        color: `hsl(${(index * 137.5) % 360}, 70%, 50%)`, // Generate different colors
      };
    });
  }, [reportData]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Weekly Time Summary
      </Typography>

      {reportData.length === 0 ? (
        <Typography variant="body1" color="text.secondary">
          No data available for the selected filters.
        </Typography>
      ) : (
        <Box>
          {/* Chart Section */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ width: "100%", height: 400 }}>
              <ResponsiveContainer>
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, value }) => `${name}: ${value}h`}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={value => [`${value} hours`, "Duration"]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </Box>

          {/* Detailed Data Section */}
          <Typography variant="h6" gutterBottom>
            Detailed Breakdown
          </Typography>
          {reportData.map(project => (
            <Accordion key={project.project_id} sx={{ mb: 1 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: "flex", justifyContent: "space-between", width: "100%", pr: 2 }}>
                  <Typography variant="h6">{project.project_name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total: {getTotalProjectDuration(project.users)} ({project.users.length} user
                    {project.users.length !== 1 ? "s" : ""})
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>User</TableCell>
                        <TableCell align="right">Duration</TableCell>
                        <TableCell align="right">Total Seconds</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {project.users.map(user => (
                        <TableRow key={user.user_id}>
                          <TableCell>{user.full_name}</TableCell>
                          <TableCell align="right">{user.total_duration}</TableCell>
                          <TableCell align="right">{user.total_duration_second.toLocaleString()}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}
    </Box>
  );
}

export default WeeklySummary;
