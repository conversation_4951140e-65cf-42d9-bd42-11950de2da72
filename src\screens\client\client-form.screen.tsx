import React from "react";
import { Box, Button, TextField, FormControl, Select, MenuItem, FormHelperText } from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import { Client } from "@/interfaces/client.interface";
import { ClientFormData, clientStatusLabels, UpdateClientFormData } from "@/interfaces/client.interface";
import { Grid } from "@mui/material";
import theme from "@/theme/theme";

interface ClientFormProps {
  selectedClient?: Client | null;
  onSubmit: (data: ClientFormData | UpdateClientFormData) => void;
  isSubmitting: boolean;
  onClose: () => void;
}

const validationSchema = yup.object().shape({
  name: yup.string().required("Name is required"),
  status: yup.string().required("Status is required"),
  email: yup.string().email("Invalid email"),
});

const ClientForm: React.FC<ClientFormProps> = ({ selectedClient, onSubmit, isSubmitting }) => {
  const formik = useFormik({
    initialValues: {
      name: selectedClient?.name || "",
      description: selectedClient?.description || "",
      email: selectedClient?.email || "",
      address: selectedClient?.address || "",
      country: selectedClient?.country || "",
      phone: selectedClient?.phone || "",
      status: selectedClient?.status || "",
    },
    enableReinitialize: true,
    validationSchema,
    onSubmit: (values: ClientFormData) => {
      if (selectedClient?.id) {
        onSubmit({ id: selectedClient.id, ...values });
      } else {
        onSubmit(values);
      }
    },
  });

  return (
    <Box sx={{ mt: 1 }}>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={2}>
          <Grid size={6}>
            <TextField
              fullWidth
              name="name"
              value={formik.values.name}
              variant="outlined"
              onChange={formik.handleChange}
              error={formik.touched.name && !!formik.errors.name}
              helperText={formik.touched.name && formik.errors.name}
              label="Client Name"
              slotProps={{
                inputLabel: {
                  sx: {
                    color: "gray",
                    "&.Mui-focused": {
                      color: "gray",
                    },
                  },
                },
              }}
            ></TextField>
          </Grid>
          <Grid size={6}>
            <TextField
              fullWidth
              name="address"
              value={formik.values.address}
              variant="outlined"
              onChange={formik.handleChange}
              error={formik.touched.address && !!formik.errors.address}
              helperText={formik.touched.address && formik.errors.address}
              label="Address"
              slotProps={{
                inputLabel: {
                  sx: {
                    color: "gray",
                    "&.Mui-focused": {
                      color: "gray",
                    },
                  },
                },
              }}
            ></TextField>
          </Grid>
          <Grid size={6}>
            <TextField
              fullWidth
              name="country"
              value={formik.values.country}
              variant="outlined"
              onChange={formik.handleChange}
              error={formik.touched.country && !!formik.errors.country}
              helperText={formik.touched.country && formik.errors.country}
              label="Country"
              slotProps={{
                inputLabel: {
                  sx: {
                    color: "gray",
                    "&.Mui-focused": {
                      color: "gray",
                    },
                  },
                },
              }}
            ></TextField>
          </Grid>
          <Grid size={6}>
            <TextField
              fullWidth
              name="phone"
              value={formik.values.phone}
              variant="outlined"
              onChange={formik.handleChange}
              error={formik.touched.phone && !!formik.errors.phone}
              helperText={formik.touched.phone && formik.errors.phone}
              label="Phone"
              slotProps={{
                inputLabel: {
                  sx: {
                    color: "gray",
                    "&.Mui-focused": {
                      color: "gray",
                    },
                  },
                },
              }}
            ></TextField>
          </Grid>

          <Grid size={12}>
            <TextField
              fullWidth
              multiline
              rows={4}
              name="description"
              value={formik.values.description}
              variant="outlined"
              onChange={formik.handleChange}
              error={formik.touched.description && !!formik.errors.description}
              helperText={formik.touched.description && formik.errors.description}
              label="Description"
              slotProps={{
                inputLabel: {
                  sx: {
                    color: "gray",
                    "&.Mui-focused": {
                      color: "gray",
                    },
                  },
                },
              }}
            ></TextField>
          </Grid>
          <Grid size={6}>
            <TextField
              fullWidth
              name="email"
              value={formik.values.email}
              variant="outlined"
              onChange={formik.handleChange}
              error={formik.touched.email && !!formik.errors.email}
              helperText={formik.touched.email && formik.errors.email}
              label="Email"
              slotProps={{
                inputLabel: {
                  sx: {
                    color: "gray",
                    "&.Mui-focused": {
                      color: "gray",
                    },
                  },
                },
              }}
            ></TextField>
          </Grid>
          <Grid size={6}>
            <FormControl fullWidth error={formik.touched.status && !!formik.errors.status}>
              <Select
                name="status"
                onChange={formik.handleChange}
                value={formik.values.status}
                displayEmpty
                renderValue={selected => {
                  if (!selected) {
                    return (
                      <span
                        style={{
                          color: theme.palette.custom.brand.silverChalice,
                        }}
                      >
                        Select Status
                      </span>
                    );
                  }
                  return clientStatusLabels[selected as keyof typeof clientStatusLabels] || "Unknown";
                }}
              >
                <MenuItem value="" disabled>
                  Select status
                </MenuItem>
                {Object.entries(clientStatusLabels).map(([value, label]) => (
                  <MenuItem key={value} value={value}>
                    {label}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.status && formik.errors.status && <FormHelperText>{formik.errors.status}</FormHelperText>}
            </FormControl>
          </Grid>
          <Grid size={12}>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              disabled={isSubmitting}
              loading={isSubmitting}
              loadingPosition="end"
            >
              {isSubmitting ? (selectedClient ? "Updating..." : "Creating...") : selectedClient ? "Update" : "Create"}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default ClientForm;
