import React from "react";
import { Box, Switch, FormControlLabel, SelectChangeEvent } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import AutocompleteInfinite from "@/components/common/AutocompleteInfinite";
import { useProjects } from "@/hooks/tracker/useProjects";
import { useUsersInfinite } from "@/hooks/tracker/useUsersInfinite";
import { ProjectOption } from "@/types/tracker";
import { UserOption } from "@/hooks/tracker/useUsersInfinite";
import { ReportFiltersProps } from "@/types/reports";

// Move hook wrappers outside component for performance
const useProjectOptions = (searchTerm: string) => {
  const { projectOptions, ...rest } = useProjects(searchTerm);
  return { options: projectOptions, ...rest };
};

const useUserOptions = (searchTerm: string) => {
  const { userOptions, ...rest } = useUsersInfinite(searchTerm);
  return { options: userOptions, ...rest };
};

const ReportFilters: React.FC<ReportFiltersProps> = ({
  dateRange,
  setDateRange,
  selectedProjects,
  handleProjectsChange,
  selectedUsers,
  handleUsersChange,
  billableFilter,
  setBillableFilter,
  otFilter,
  setOtFilter,
}) => {
  // Adapter functions to transform AutocompleteInfinite's newValue to SelectChangeEvent-like format
  const handleProjectsAutocompleteChange = (newValue: string[]) => {
    handleProjectsChange({ target: { value: newValue } } as SelectChangeEvent<string[]>);
  };
  const handleUsersAutocompleteChange = (newValue: string[]) => {
    handleUsersChange({ target: { value: newValue } } as SelectChangeEvent<string[]>);
  };

  // Responsive layout: allow wrapping and balance autocomplete boxes
  return (
    <Box sx={{ display: "flex", gap: 2, mb: 3, alignItems: "center", flexWrap: "wrap" }}>
      <DatePicker
        label="Start Date"
        value={dateRange.start}
        onChange={newValue => setDateRange({ ...dateRange, start: newValue || new Date() })}
        sx={{ minWidth: 180 }}
      />
      <DatePicker
        label="End Date"
        value={dateRange.end}
        onChange={newValue => setDateRange({ ...dateRange, end: newValue || new Date() })}
        sx={{ minWidth: 180 }}
      />
      <Box sx={{ minWidth: 220, flexGrow: 1, flexBasis: 0 }}>
        <AutocompleteInfinite<ProjectOption>
          value={selectedProjects}
          onChange={handleProjectsAutocompleteChange}
          useInfiniteOptions={useProjectOptions}
          label="Projects"
          placeholder="Search projects..."
        />
      </Box>
      <Box sx={{ minWidth: 220, flexGrow: 1, flexBasis: 0 }}>
        <AutocompleteInfinite<UserOption>
          value={selectedUsers}
          onChange={handleUsersAutocompleteChange}
          useInfiniteOptions={useUserOptions}
          label="Users"
          placeholder="Search users..."
        />
      </Box>
      <FormControlLabel
        control={<Switch checked={billableFilter} onChange={e => setBillableFilter(e.target.checked)} />}
        label="Billable Only"
      />
      <FormControlLabel
        control={<Switch checked={otFilter} onChange={e => setOtFilter(e.target.checked)} />}
        label="OT Only"
      />
    </Box>
  );
};

export default ReportFilters;
