import {
  Avatar,
  Box,
  Button,
  Paper,
  Typography,
  MenuItem,
  Select,
  IconButton,
  InputBase,
  SelectChangeEvent,
  Switch,
  FormControl,
  FormGroup,
  FormControlLabel,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import * as Yup from "yup";
import { useState } from "react";
import GenericModal from "@/components/modal/generic-modal.component";
import TableComponent from "@/components/table/table.component";
import { useMutation, useQuery } from "@tanstack/react-query";
import TeamAPI from "@/core/api/teams.service";
import { GridColDef, GridPaginationModel, GridRenderCellParams } from "@mui/x-data-grid";
import TeamMemberForm from "@/screens/teams/team-member-form.screen";
import { Formik } from "formik";
import Resource from "@/core/api/resource";
import { Group, IEditUserPayload, ITeam, IUserForm, IUserInvitePayload } from "@/interfaces/teams.interface";
import ToastMessage from "@/components/toast/toast.component";
import { AxiosError, AxiosResponse } from "axios";
import TruncatedText from "@/components/truncatetext/truncatedText";
import { IReinviteErrorResponse, IUserReinvitePayload, MappedTeamMember, statusLabels } from "@/types/teams";
import { GridFeatureMode } from "@/enums/enum.ts";
import ActiveBreadcrumb from "@/components/breadcrumbs/activeBreadcrumb.tsx";

export const TeamMembers = () => {
  const teamResources = new TeamAPI.TeamResources();
  const userInviteResource = new Resource("users/invite");
  const userResource = new Resource("users");
  const userReinviteResource = new Resource("users/reinvite");

  const [filter, setFilter] = useState({
    page: 1,
    "page-size": 10,
    ordering: "",
    search: "",
    status: "all",
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUserData, setSelectedUserData] = useState<ITeam | null>(null);

  const initialValues = {
    first_name: selectedUserData?.first_name || "",
    middle_name: selectedUserData?.middle_name || "",
    last_name: selectedUserData?.last_name || "",
    email: selectedUserData?.email || "",
    groups: selectedUserData?.groups || [],
    country: selectedUserData?.country || [],
    phone: selectedUserData?.phone || [],
    is_active: selectedUserData?.is_active,
  };

  const validationSchema = Yup.object().shape({
    first_name: Yup.string().required("First name is required"),
    middle_name: Yup.string().optional(),
    last_name: Yup.string().required("Last name is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    groups: Yup.array().required("Group is required"),
    country: Yup.string().required("Country is required"),
    phone: Yup.number().required("Phone is required"),
  });

  const {
    data: membersList,
    isLoading: membersLoading,
    refetch: refetchMemberList,
  } = useQuery({
    queryKey: ["userTeams", filter],
    queryFn: () => teamResources.list(filter),
    placeholderData: undefined,
  });

  const handleRowEdit = async (data: ITeam) => {
    setSelectedUserData(data);
    setIsModalOpen(true);
    await refetchMemberList();
    return data;
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilter({
      ...filter,
      search: event.target.value,
    });
  };

  const handleStatusChange = (event: SelectChangeEvent<string>) => {
    setFilter({
      ...filter,
      status: event.target.value,
    });
  };

  let rows: MappedTeamMember[] =
    membersList?.data?.results?.map((user: ITeam) => ({
      id: user.id,
      first_name: user.first_name,
      middle_name: user.middle_name,
      last_name: user.last_name,
      name: `${user.first_name}  ${user?.middle_name ? user.middle_name : ""} ${user.last_name}`,
      email: user.email,
      is_active: user.is_active,
      is_invited: user.is_invited,
      groups: user.groups,
      profile_image: user.detail?.image,
      phone: user.detail?.phone,
      country: user.detail?.country,
    })) || [];

  if (filter.status === "active") {
    rows = rows.filter((row: MappedTeamMember) => row.is_active);
  } else if (filter.status === "inactive") {
    rows = rows.filter((row: MappedTeamMember) => !row.is_active);
  } else if (filter.status === "invited") {
    rows = rows.filter((row: MappedTeamMember) => row.is_invited);
  }

  const columns: GridColDef<ITeam>[] = [
    {
      field: "name",
      headerName: "Name",
      sortable: false,
      filterable: false,
      flex: 2,
      renderCell: (params: GridRenderCellParams<ITeam>) => {
        const { name, profile_image } = params.row;
        return (
          <>
            <Avatar alt={name} src={profile_image} sx={{ width: 24, height: 24 }} />
            <Box sx={{ textTransform: "capitalize", mx: 1 }}>
              <TruncatedText text={name} />
            </Box>
          </>
        );
      },
    },
    {
      field: "email",
      headerName: "Email",
      flex: 2,
      sortable: true,
      filterable: true,
    },
    {
      field: "groups",
      headerName: "Groups",
      flex: 2,
      renderCell: (params: GridRenderCellParams<ITeam, Group[]>) => params.value?.map(g => g.name_display).join(", "),
    },
    {
      field: "groupStatus",
      headerName: "Member Status",
      flex: 2,
      renderCell: (params: GridRenderCellParams<ITeam>) => {
        const isActive = params.row.is_active;

        const handleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
          const updatedValue = event.target.checked;

          const payload: Partial<IEditUserPayload> = {
            id: params.row.id,
            is_active: updatedValue,
          };
          userUpdateMutation.mutate(payload);
        };

        return (
          <FormControl component="fieldset" variant="standard">
            <FormGroup>
              <FormControlLabel
                control={<Switch checked={isActive} onChange={handleToggle} name={`switch-${params.row.id}`} />}
                label=""
              />
            </FormGroup>
          </FormControl>
        );
      },
    },
  ];

  const handlePaginationChange = (model: GridPaginationModel) => {
    setFilter({
      ...filter,
      page: model.page + 1,
      "page-size": model.pageSize,
    });
  };

  const handleClose = () => {
    setIsModalOpen(false);
  };

  const handleCreateUser = () => {
    setIsModalOpen(true);
    setSelectedUserData(null);
  };

  const handleUserReInvite = (data: ITeam) => {
    handleUserReInviteMutation.mutate({ user_id: data.id });
  };

  const userInviteMutation = useMutation({
    mutationFn: (data: IUserInvitePayload) => userInviteResource.store(data),
    onSuccess: async () => {
      setIsModalOpen(false);
      ToastMessage.success("User created successfully");
      await refetchMemberList();
    },
    onError: (error: AxiosError) => {
      ToastMessage.error(error.message);
    },
  });

  const handleUserReInviteMutation = useMutation({
    mutationFn: (payload: IUserReinvitePayload) => userReinviteResource.store(payload),
    onSuccess: (res: AxiosResponse) => {
      if (res && res.data && res.data.success) {
        ToastMessage.success(res.data.success);
      } else {
        ToastMessage.success("Reinvite email sent successfully.");
      }
    },
    onError: (error: AxiosError) => {
      if (error.response && error.response.data) {
        const errorData: IReinviteErrorResponse = error.response.data;
        if (errorData.error && Array.isArray(errorData.error) && errorData.error.length > 0) {
          ToastMessage.error(errorData.error[0] || "An unexpected error occurred.");
        } else if (errorData.message) {
          ToastMessage.error(errorData.message);
        } else {
          ToastMessage.error("An unexpected error occurred.");
        }
      } else {
        ToastMessage.error(error.message);
      }
    },
  });

  const userUpdateMutation = useMutation({
    mutationFn: (payload: Partial<IEditUserPayload>) => {
      if (!payload.id) {
        throw new Error("User ID is required for update");
      }
      return userResource.patch(payload.id, payload);
    },
    onSuccess: async () => {
      setIsModalOpen(false);
      await refetchMemberList();
      setSelectedUserData(null);
      ToastMessage.success("User Updated successfully");
    },
    onError: (error: AxiosError) => {
      ToastMessage.error(error.message);
    },
  });

  const handelSubmit = async (values: IUserForm) => {
    if (!selectedUserData) {
      const payloadDataForCreate = {
        ...values,
        groups: values.groups.map((g: Group) => g.id),
      };
      userInviteMutation.mutate(payloadDataForCreate);
    } else {
      const payloadDataForEdit: IEditUserPayload = {
        id: selectedUserData.id,
        first_name: values.first_name,
        last_name: values.last_name,
        middle_name: values.middle_name,
        email: values.email,
        group_ids: values.groups.map(g => (typeof g === "object" && g !== null ? g.id : g)),
        phone: values.phone,
        country: values.country,
        is_active: !!values.is_active,
      };
      userUpdateMutation.mutate(payloadDataForEdit);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <ActiveBreadcrumb
            color="inherit"
            links={[
              { text: "Tracker", link: "/tracker" },
              { text: "Teams", link: "/teams/members" },
              { text: "Members", link: "/teams/members" },
            ]}
          />
        </Box>
      </Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" fontWeight={600}>
          Members
        </Typography>
        <Button onClick={handleCreateUser} variant="contained" color="primary">
          + Create User
        </Button>
      </Box>
      <Box
        sx={{
          p: 0,
          mb: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
          flexWrap: "nowrap",
        }}
      >
        <Paper component="div">
          <Select
            value={filter.status}
            onChange={handleStatusChange}
            displayEmpty
            size="small"
            fullWidth
            sx={{
              height: "100%",
              minWidth: 140,
              "& .MuiSelect-select": {
                display: "flex",
                alignItems: "center",
                height: "100%",
                boxSizing: "border-box",
              },
            }}
          >
            <MenuItem value="all">
              <em>All Status</em>
            </MenuItem>
            {Object.entries(statusLabels).map(([value, label]) => (
              <MenuItem key={value} value={value}>
                {label}
              </MenuItem>
            ))}
          </Select>
        </Paper>

        <Paper
          component="form"
          sx={{
            p: "2px 4px",
            display: "flex",
            alignItems: "center",
            flex: 1,
            height: 40,
          }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Search..."
            inputProps={{ "aria-label": "search" }}
            value={filter.search}
            onChange={handleSearchChange}
          />
          <IconButton type="button" sx={{ p: "10px" }} aria-label="search">
            <SearchIcon />
          </IconButton>
        </Paper>
      </Box>
      <TableComponent
        columns={columns}
        rows={rows}
        loading={membersLoading}
        hideFooter={false}
        contentAlign="left"
        rowCount={membersList?.data?.count}
        paginationMode={GridFeatureMode.SERVER}
        sortingMode={GridFeatureMode.SERVER}
        onPaginationChange={handlePaginationChange}
        actions={[
          {
            label: "Edit",
            handler: data => handleRowEdit(data as ITeam),
          },
          {
            label: "Reinvite",
            handler: data => handleUserReInvite(data as ITeam),
            shouldShow: row => !row.is_active && row.is_invited,
          },
        ]}
      />

      <GenericModal
        setting={{ open: isModalOpen, onClose: handleClose }}
        title={selectedUserData ? "Edit User" : "Create User"}
        height="500px"
      >
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={values => handelSubmit(values as IUserForm)}
        >
          {formikProps => (
            <form onSubmit={formikProps.handleSubmit}>
              <TeamMemberForm openEdit={isModalOpen} selectedData={selectedUserData} />
            </form>
          )}
        </Formik>
      </GenericModal>
    </Box>
  );
};
