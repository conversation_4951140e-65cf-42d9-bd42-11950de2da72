import React from "react";
import { DashboardLayout } from "@toolpad/core/DashboardLayout";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { AppProvider, type Session } from "@toolpad/core/AppProvider";
import theme from "@/theme/theme";

import HourglassTopIcon from "@mui/icons-material/HourglassTop";
import DashboardIcon from "@mui/icons-material/Dashboard";
import AssessmentIcon from "@mui/icons-material/Assessment";
import PeopleAltIcon from "@mui/icons-material/PeopleAlt";
import ContactsIcon from "@mui/icons-material/Contacts";
import { default as ProjectTypeIcon } from "@mui/icons-material/AccountTree";
import { default as ProjectIcon } from "@mui/icons-material/Work";
import TeamsScreen from "@/screens/teams/teams.screen";

import { useQuery, useMutation } from "@tanstack/react-query";
import Resource from "@/core/api/resource";
import { dispatchClearUser } from "@/store/manager";
import { NavigateOptions } from "@/types";
import GroupsIcon from "@mui/icons-material/Groups";
import Diversity3Icon from "@mui/icons-material/Diversity3";
import SettingsIcon from "@mui/icons-material/Settings";
import Avatar from "@mui/material/Avatar";

const NAVIGATION = [
  {
    segment: "tracker",
    title: "Tracker",
    icon: <HourglassTopIcon />,
  },
  {
    segment: "dashboard",
    title: "Dashboard",
    icon: <DashboardIcon />,
  },
  {
    segment: "reports",
    title: "Reports",
    icon: <AssessmentIcon />,
  },
  {
    segment: "clients",
    title: "Clients",
    icon: <ContactsIcon />,
  },
  {
    segment: "projects",
    title: "Projects",
    icon: <ProjectIcon />,
    children: [
      {
        segment: "",
        title: "Projects",
        icon: <ProjectIcon />,
      },
      {
        segment: "types",
        title: "Project Types",
        icon: <ProjectTypeIcon />,
      },
    ],
  },
  {
    segment: "teams",
    title: "Teams",
    icon: <PeopleAltIcon />,
    element: <TeamsScreen />,
    children: [
      {
        segment: "members",
        title: "Members",
        icon: <Diversity3Icon />,
      },
      {
        segment: "groups",
        title: "Groups",
        icon: <GroupsIcon />,
      },
    ],
  },
  {
    segment: "settings",
    title: "Settings",
    icon: <SettingsIcon />,
    children: [
      {
        segment: "public-profile",
        title: "profile",
        icon: <Avatar sx={{ width: 24, height: 24 }} />,
      },
      {
        segment: "integrations",
        title: "Integrations",
        icon: <SettingsIcon />,
      },
    ],
  },
];

const BRANDING = {
  title: "Freetime",
  // logo: <img src={"/logo-path-here"} alt="freetime" style={{ height: 60, marginLeft: 20 }} />,
  homeUrl: "/",
};

const CustomDashboardLayout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const navigateFn = useNavigate();

  // User profile fetch
  const userProfileQuery = async () => {
    const userResource = new Resource("users/my-profile");
    const response = await userResource.list();
    return response.data;
  };

  const { data: user } = useQuery({
    queryKey: ["userProfile"],
    queryFn: userProfileQuery,
  });

  // Auth logout
  const logoutResource = new Resource("auth/logout");
  const logoutMutation = useMutation({
    mutationFn: () => logoutResource.store({}),
    onSuccess: () => {
      dispatchClearUser();
      navigate("/login");
    },
  });

  //  Router
  const router = {
    pathname: location.pathname,
    searchParams: new URLSearchParams(location.search),
    navigate: (to: string | URL, options: unknown) => {
      const url = typeof to === "string" ? to : to.toString();
      const safeOptions = options && typeof options === "object" ? (options as NavigateOptions) : {};

      navigateFn(url, {
        replace: safeOptions?.replace,
        state: safeOptions?.state,
      });
    },
  };

  // Session
  const fullName = `${user?.first_name ?? ""} ${user?.last_name ?? ""}`.trim();
  const session: Session | null = React.useMemo(() => {
    return user
      ? {
          user: {
            name: (
              <span style={{ cursor: "pointer", textDecoration: "underline" }} onClick={() => navigate("/profile")}>
                {fullName}
              </span>
            ) as unknown as string,
            email: user?.email,
            image: user?.image_url_full || user?.detail?.image || "",
          },
        }
      : null;
  }, [user, fullName, navigate]);

  //  Auth methods
  const authentication = React.useMemo(() => {
    return {
      signIn: () => {
        // Not used in this layout
      },
      signOut: () => {
        logoutMutation.mutate();
      },
    };
  }, [logoutMutation]);

  return (
    <AppProvider
      navigation={NAVIGATION}
      theme={theme}
      branding={BRANDING}
      session={session}
      authentication={authentication}
      router={router}
    >
      <DashboardLayout defaultSidebarCollapsed sidebarExpandedWidth={250}>
        <Outlet />
      </DashboardLayout>
    </AppProvider>
  );
};

export default CustomDashboardLayout;
