import GenericModal from "@/components/modal/generic-modal.component";
import TableComponent from "@/components/table/table.component";
import ToastMessage from "@/components/toast/toast.component";
import Resource from "@/core/api/resource";
import { IGroup } from "@/interfaces/teams.interface";
import { Box, Button, Typography } from "@mui/material";
import { GridColDef, GridPaginationModel } from "@mui/x-data-grid";
import { Formik } from "formik";
import { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import * as Yup from "yup";
import TeamGroupForm from "./team-group-form.screen";
import BootstrapDialogBox from "@/components/common/bootstrap-dialog";
import { AxiosError } from "axios";
import ManagePermissions from "./assign-permissions.screen";
import { useNavigate } from "react-router-dom";
import ActiveBreadcrumb from "@/components/breadcrumbs/activeBreadcrumb.tsx";

const TeamsGroup = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const groupResource = new Resource("auth/groups");
  const [indGroupData, setIndGroupData] = useState<IGroup | null>(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<IGroup | null>(null);

  const navigate = useNavigate();

  const [filter, setFilter] = useState({
    page: 1,
    "page-size": 10,
    ordering: "",
    search: "",
  });

  const initialValues = {
    name: indGroupData?.name || "",
  };

  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Group name is required"),
  });

  const {
    data: groupList,
    isLoading: groupListLoading,
    refetch: groupListRefetch,
  } = useQuery({
    queryKey: ["userGroup", filter],
    queryFn: () => groupResource.list(filter),
    placeholderData: undefined,
  });

  const rows =
    groupList?.data?.results?.map((group: IGroup) => ({
      id: group.id,
      name: group.name,
      name_display: group.name_display || group.name,
    })) || [];

  const columns: GridColDef<IGroup>[] = [
    {
      field: "name_display",
      headerName: "Group Name",
      flex: 2,
      sortable: true,
      filterable: true,
    },
  ];

  const handlePaginationChange = (model: GridPaginationModel) => {
    setFilter({
      ...filter,
      page: model.page + 1,
      "page-size": model.pageSize,
    });
  };

  const handleClose = () => {
    setIsModalOpen(false);
    setIndGroupData(null);
  };

  const handleRowEdit = (data: IGroup) => {
    setIndGroupData(data);
    setIsModalOpen(true);
    groupListRefetch();
    return data;
  };

  const handleRowDelete = async (data: IGroup) => {
    setSelectedGroup(data);
    setOpenDeleteDialog(true);
  };

  const handleGroupCreate = () => {
    setIsModalOpen(true);
    setIndGroupData(null);
  };

  const confirmDelete = async () => {
    if (!selectedGroup) return;
    deleteGroupMutation.mutate(selectedGroup.id);
  };

  const addGroupMutation = useMutation({
    mutationFn: (data: IGroup) => groupResource.store(data),
    onSuccess: () => {
      setIsModalOpen(false);
      ToastMessage.success("Group created successfully");
      groupListRefetch();
    },
    onError: (error: AxiosError) => {
      ToastMessage.error(error.message);
    },
  });

  const updateGroupMutation = useMutation({
    mutationFn: (data: IGroup) => groupResource.patch(data.id, data),
    onSuccess: () => {
      setIsModalOpen(false);
      ToastMessage.success("Group Updated successfully");
      groupListRefetch();
    },
    onError: (error: AxiosError) => {
      ToastMessage.error(error.message);
    },
  });

  const deleteGroupMutation = useMutation({
    mutationFn: (id: string) => groupResource.destroy(id),
    onSuccess: () => {
      ToastMessage.success("Group deleted successfully");
      groupListRefetch();
      setOpenDeleteDialog(false);
      setSelectedGroup(null);
    },
    onError: (error: AxiosError) => {
      ToastMessage.error(error.message);
    },
  });

  const handelSubmit = async (values: IGroup) => {
    if (!indGroupData?.name) {
      addGroupMutation.mutate(values);
    } else {
      updateGroupMutation.mutate({
        ...values,
        id: indGroupData.id,
      });
    }
  };

  const handleViewMember = (data: IGroup) => {
    navigate(`/teams/groups/view/${data.id}`);
  };

  //Assign permission
  const [openAssignPermissionForm, setOpenAssignPermissionForm] = useState(false);
  const handleRowPermissionAssign = (data: IGroup) => {
    setSelectedGroup(data);
    setOpenAssignPermissionForm(true);
  };
  const handlePermissionAssignClose = () => {
    setOpenAssignPermissionForm(false);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <ActiveBreadcrumb
            color="inherit"
            links={[
              { text: "Tracker", link: "/tracker" },
              { text: "Teams", link: "/teams/group" },
              { text: "Members", link: "/teams/group" },
            ]}
          />
        </Box>
      </Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" fontWeight={600}>
          Groups
        </Typography>
        <Button onClick={handleGroupCreate} variant="contained" color="primary">
          + Create Group
        </Button>
      </Box>

      <TableComponent
        columns={columns}
        rows={rows}
        loading={groupListLoading}
        hideFooter={false}
        contentAlign="left"
        rowCount={groupList?.data?.count}
        onPaginationChange={model => {
          handlePaginationChange(model);
        }}
        actions={[
          {
            label: "View Groups",
            handler: data => {
              handleViewMember(data as IGroup);
            },
          },
          {
            label: "Manage Permission",
            handler: data => {
              handleRowPermissionAssign(data as IGroup);
            },
          },
          {
            label: "Edit",
            handler: data => {
              handleRowEdit(data as IGroup);
            },
          },
          {
            label: "Delete",
            handler: data => {
              handleRowDelete(data as IGroup);
            },
          },
        ]}
      />

      <GenericModal
        setting={{ open: isModalOpen, onClose: handleClose }}
        title={indGroupData ? "Edit User" : "Create User"}
        height="500px"
      >
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={values => {
            handelSubmit(values as IGroup);
          }}
        >
          {formikProps => (
            <form onSubmit={formikProps.handleSubmit}>
              <TeamGroupForm openEdit={isModalOpen} />
            </form>
          )}
        </Formik>
      </GenericModal>
      <GenericModal
        setting={{
          open: openAssignPermissionForm,
          onClose: handlePermissionAssignClose,
        }}
        title={`Manage Permission:${selectedGroup?.name}`}
        height="500px"
      >
        {selectedGroup && <ManagePermissions group={selectedGroup} />}
      </GenericModal>

      <BootstrapDialogBox open={openDeleteDialog} onClose={() => setOpenDeleteDialog(false)} title="Confirm Delete">
        <Box>
          <Typography variant="body1">
            Are you sure you want to delete{" "}
            <Typography component="span" fontWeight="bold">
              {selectedGroup?.name}
            </Typography>
            ?
          </Typography>

          <Box mt={3} display="flex" justifyContent="flex-end" gap={2}>
            <Button onClick={() => setOpenDeleteDialog(false)} variant="outlined">
              Cancel
            </Button>
            <Button onClick={confirmDelete} variant="contained" color="error">
              Delete
            </Button>
          </Box>
        </Box>
      </BootstrapDialogBox>
    </Box>
  );
};

export default TeamsGroup;
