import { useEffect, useState } from "react";
import { Box, Typography, Button, Select, MenuItem, Paper, IconButton, InputBase } from "@mui/material";
import ActiveBreadcrumb from "@/components/breadcrumbs/activeBreadcrumb";
import { IActiveBreadcrumbProps } from "@/interfaces/activeBreadcrumbProps.interface";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import Resource from "@/core/api/resource";
import { Project, ProjectFormData, ProjectType, statusLabels } from "@/interfaces/project.interface";
import { useMutation, useQuery, useQueryClient, keepPreviousData } from "@tanstack/react-query";
import TableComponent from "@/components/table/table.component";
import { GridColDef, GridFilterModel, GridPaginationModel, GridSortModel } from "@mui/x-data-grid";
import { useNavigate } from "react-router-dom";
import { GridFeatureMode } from "@/enums/enum";
import ToastMessage from "@/components/toast/toast.component";

import SearchIcon from "@mui/icons-material/Search";
import theme from "@/theme/theme";
import ProjectForm from "./project.form.screen";

const ProjectsListScreen = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [projectQueryParams, setProjectQueryParams] = useState({
    page: 1,
    pageSize: 10,
    ordering: "updated_at",
    search: "",
    status: "",
  });
  const [searchTerm, setSearchTerm] = useState(projectQueryParams.search);

  const projectResource = new Resource("projects");
  const projectTypeResource = new Resource("projects/types");

  useEffect(() => {
    const handler = setTimeout(() => {
      setProjectQueryParams(prev => ({
        ...prev,
        search: searchTerm,
      }));
    }, 500);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  const trackerPageBreadCrumbs: IActiveBreadcrumbProps = {
    color: theme.palette.primary.main,
    links: [
      {
        text: "Projects",
        link: "/projects",
        customColor: theme.palette.text.primary,
      },
    ],
  };

  const query = {
    page: projectQueryParams.page,
    "page-size": projectQueryParams.pageSize,
    ordering: projectQueryParams.ordering,
    search: projectQueryParams.search,
    status: projectQueryParams.status,
  };

  const {
    //allProjectlist
    data: allProjectlist,
    isLoading,
  } = useQuery({
    queryKey: ["projectlist", JSON.stringify(query)],
    queryFn: async () => {
      const response = await projectResource.list(query);
      return {
        rows: response.data.results,
        count: response.data.count,
      };
    },
    placeholderData: keepPreviousData,
  });

  const {
    //projectTypes
    data: projectTypes,
  } = useQuery<ProjectType[]>({
    queryKey: ["projecttypes"],
    queryFn: async () => {
      const response = await projectTypeResource.list({ "page-size": 100 });
      return response.data.results;
    },
    placeholderData: keepPreviousData,
  });

  const [isCreateUpdateModelOpen, setIsCreateUpdateModelOpen] = useState(false);
  const openProjectCreateModel = () => {
    setIsCreateUpdateModelOpen(true);
  };
  const handleCloseModel = () => {
    setIsCreateUpdateModelOpen(false);
    setSelectedProject(null);
  };

  const rows = allProjectlist?.rows;
  const columns: GridColDef<(typeof rows)[number]>[] = [
    {
      field: "name",
      headerName: "Project Name",
      sortable: true,
      filterable: true,
    },
    {
      field: "is_public",
      headerName: "Privacy",
      sortable: true,
      filterable: true,
      valueFormatter: is_public => (is_public ? "Public" : "Private"),
    },
    {
      field: "is_billable",
      headerName: "Billable",
      sortable: true,
      filterable: true,
      valueFormatter: is_billable => (is_billable ? "Billable" : "Non Billable"),
    },
    {
      field: "status",
      headerName: "Status",
      sortable: true,
      filterable: true,
      valueFormatter: status => {
        return statusLabels[status as keyof typeof statusLabels] || "Unknown";
      },
    },
    {
      field: "project_type",
      headerName: "Project Type",
      sortable: true,
      filterable: true,
      valueFormatter: (project_type: ProjectType) => {
        const matchedType = projectTypes?.find(type => type.id === project_type?.id);
        return matchedType?.name || "-";
      },
    },
  ];

  // View
  const handleRowView = (project: Project) => {
    navigate(`/projects/${project.id}`);
  };
  // View

  // Edit
  const handleRowEdit = (project: Project) => {
    setSelectedProject(project);
    setIsCreateUpdateModelOpen(true);
  };
  // Edit

  //Project Delete
  const [isDeleteModelOpen, setIsDeleteModelOpen] = useState(false);
  const { mutate: deleteProjectMutation, isPending: deleteInProgress } = useMutation({
    mutationKey: ["deleteProject"],
    mutationFn: async (id: string) => {
      return await projectResource.destroy(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projectlist"] });
      ToastMessage.success("Project deleted successfully.");
      handleClose();
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
      handleClose();
    },
  });

  const handleRowDelete = (project: Project) => {
    setSelectedProject(project);
    setIsDeleteModelOpen(true);
  };
  const handleClose = () => {
    setSelectedProject(null);
  };

  //Project Create/Update

  // Project Create Mutation
  const { mutate: createProjectMutation, isPending: createProjectLoading } = useMutation({
    mutationKey: ["createProjectType"],
    mutationFn: async (data: ProjectFormData) => {
      return await projectResource.store(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projectlist"] });
      ToastMessage.success("Project created successfully.");
      setIsCreateUpdateModelOpen(false);
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
    },
  });

  // Project Update Mutation

  const { mutate: updateProjectMutation, isPending: updateProjectLoading } = useMutation({
    mutationKey: ["updateProjectMutation"],
    mutationFn: async (data: Partial<ProjectFormData> & { id: string }) => {
      const { id, ...rest } = data;
      return await projectResource.patch(id, rest);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projectlist"] });
      ToastMessage.success("Project updated updated successfully.");
      setIsCreateUpdateModelOpen(false);
    },
    onError: () => {
      ToastMessage.error("Something went wrong. Please try again.");
    },
  });

  //Project Sort
  const handleSortTable = (model: GridSortModel) => {
    const sortField = model[0]?.field || "";
    const sortDirection = model[0]?.sort || "";

    if (sortField && sortDirection) {
      setProjectQueryParams(prev => ({
        ...prev,
        ordering: `${sortDirection === "desc" ? "-" : ""}${sortField}`,
      }));
    }
    return model;
  };

  //Project Filter
  const handleFilterTable = (model: GridFilterModel) => {
    return model;
  };

  //Project Pagination
  const handlePaginationChange = (model: GridPaginationModel) => {
    setProjectQueryParams(prev => ({
      ...prev,
      page: model.page + 1,
      pageSize: model.pageSize,
    }));
  };
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 3,
        p: 3,
      }}
    >
      <Box>
        <ActiveBreadcrumb {...trackerPageBreadCrumbs}></ActiveBreadcrumb>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h1">Projects</Typography>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Button variant="contained" onClick={() => openProjectCreateModel()} startIcon={<AddOutlinedIcon />}>
              Create Project
            </Button>
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          p: 0,
          mb: 0,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          gap: 2,
          flexWrap: "nowrap",
        }}
      >
        <Paper component="div">
          <Select
            value={projectQueryParams.status}
            onChange={e =>
              setProjectQueryParams(prev => ({
                ...prev,
                status: e.target.value,
              }))
            }
            displayEmpty
            size="small"
            fullWidth
            sx={{
              height: "100%",
              "& .MuiSelect-select": {
                display: "flex",
                alignItems: "center",
                height: "100%",
                boxSizing: "border-box",
              },
            }}
          >
            <MenuItem value="">
              <em>All Status</em>
            </MenuItem>
            {Object.entries(statusLabels).map(([value, label]) => (
              <MenuItem key={value} value={value}>
                {label}
              </MenuItem>
            ))}
          </Select>
        </Paper>

        <Paper
          component="form"
          sx={{
            p: "2px 4px",
            display: "flex",
            alignItems: "center",
            flex: 1,
            height: 40,
          }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Search..."
            inputProps={{ "aria-label": "search" }}
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
          <IconButton type="button" sx={{ p: "10px" }} aria-label="search">
            <SearchIcon />
          </IconButton>
        </Paper>
      </Box>

      <Box>
        <TableComponent
          contentAlign="left"
          columns={columns}
          rows={rows}
          rowCount={allProjectlist?.count}
          loading={isLoading}
          hideFooter={false}
          paginationMode={GridFeatureMode.SERVER}
          sortingMode={GridFeatureMode.SERVER}
          actions={[
            {
              label: "View",
              handler: project => {
                handleRowView(project as Project);
              },
            },
            {
              label: "Edit",
              handler: project => {
                handleRowEdit(project as Project);
              },
            },
            {
              label: "Delete",
              handler: project => {
                handleRowDelete(project as Project);
              },
            },
          ]}
          onSortChange={model => {
            handleSortTable(model);
          }}
          onFilterChange={model => {
            handleFilterTable(model);
          }}
          onPaginationChange={model => {
            handlePaginationChange(model);
          }}
        />
      </Box>
      <Dialog // Delete
        open={Boolean(isDeleteModelOpen && selectedProject?.id)}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{"Confirm Project Deletion"}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description" sx={{ color: "grey.900" }}>
            Are you sure you want to delete the project <strong>{selectedProject?.name}</strong>? This action is
            irreversible and will permanently remove all associated data.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => handleClose()}>Cancel</Button>
          <Button
            onClick={() => deleteProjectMutation(selectedProject?.id ? selectedProject.id : "")}
            disabled={!selectedProject?.id || deleteInProgress}
            loading={deleteInProgress}
            loadingPosition="end"
            variant="contained"
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={isCreateUpdateModelOpen} onClose={handleCloseModel}>
        <DialogTitle>{selectedProject ? "Edit Project" : "Create Project"}</DialogTitle>
        <DialogContent>
          <Box sx={{ maxWidth: "100vw", margin: "left auto", marginTop: "7px" }}>
            <ProjectForm
              selectedProject={selectedProject}
              onClose={handleCloseModel}
              onSubmit={(data: ProjectFormData) => {
                if (selectedProject?.id) {
                  updateProjectMutation({ ...data, id: selectedProject.id });
                } else {
                  createProjectMutation({ id: "", ...data });
                }
              }}
              isSubmitting={createProjectLoading || updateProjectLoading}
            />
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default ProjectsListScreen;
