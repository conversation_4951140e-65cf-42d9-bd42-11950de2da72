import { useInfiniteQuery, keepPreviousData } from "@tanstack/react-query";
import { useMemo } from "react";
import Resource from "@/core/api/resource";
import { IUser } from "@/core/api/users.service";

interface UsersResponse {
  results: IUser[];
  next: string | null;
  previous: string | null;
  count: number;
}

export interface UserOption {
  label: string;
  value: string;
  isPending?: boolean;
}

export const useUsersInfinite = (searchTerm: string, pageSize: number = 20) => {
  const usersResource = useMemo(() => new Resource("users"), []);

  // Normalize parameters to improve cache hit rates
  const normalizedSearchTerm = searchTerm.trim();
  const normalizedPageSize = pageSize || 20;

  const query = useInfiniteQuery({
    queryKey: ["usersListInfinite", normalizedSearchTerm, normalizedPageSize],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await usersResource.list({
        search: normalizedSearchTerm,
        page: pageParam,
        page_size: normalizedPageSize,
        is_active: true, // Only fetch active users
      });
      return response.data;
    },
    enabled: true,
    placeholderData: keepPreviousData,
    retry: 3,
    retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    getNextPageParam: (lastPage: UsersResponse) => {
      if (lastPage.next) {
        const url = new URL(lastPage.next);
        return parseInt(url.searchParams.get("page") || "1");
      }
      return undefined;
    },
    initialPageParam: 1,
  });

  // Flatten all pages into single array for dropdown
  const userOptions: UserOption[] = useMemo(
    () =>
      query.data?.pages.flatMap(
        page =>
          page.results?.map((user: IUser) => ({
            label: `${user.first_name} ${user.last_name}`.trim() || user.email || "",
            value: user.id?.toString() || "",
          })) || []
      ) || [],
    [query.data?.pages]
  );

  return {
    ...query,
    userOptions,
    hasNextPage: query.hasNextPage,
    fetchNextPage: query.fetchNextPage,
    isFetchingNextPage: query.isFetchingNextPage,
    totalCount: query.data?.pages[0]?.count || 0,
    error: query.error,
    isError: query.isError,
    retry: query.refetch,
  };
};
